<img src="https://api.iconify.design/unjs:automd.svg" alt="logo" width="100" height="100" align="right" />

# wechatferry

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![github stars][github-stars-src]][github-stars-href]
[![License][license-src]][license-href]
[![WebSite][website-src]][website-href]

>[!TIP]
> 再一次让 Wechaty 免费协议(PC Hook)重放荣光 - [@wechatferry/puppet](https://wcferry.netlify.app/integrations/wechaty.html)

## 安装

```bash
pnpm add wechatferry
```

## Packages

|名称|描述|
|---|---|
|[@wechatferry/core](https://github.com/wechatferry/wechatferry/tree/main/packages/core)| 调用 sdk.dll 封装 tcp 连接 |
|[@wechatferry/agent](https://github.com/wechatferry/wechatferry/tree/main/packages/agent)| 封装核心 更易于使用 |
|[@wechatferry/puppet](https://github.com/wechatferry/wechatferry/tree/main/packages/puppet)| 让 Wechaty 免费协议重放荣光 |
|[@wechatferry/nuxt](https://github.com/wechatferry/wechatferry/tree/main/packages/nuxt)| 使 Nuxt 轻松接入微信机器人 |
|[@wechatferry/plugins](https://github.com/wechatferry/wechatferry/tree/main/packages/plugins)| 随便实现的几个插件 |

## 免责声明

使用本项目则表示您同意并认可以下声明

### 1. 使用目的

* 本项目仅供学习交流使用，**请勿用于非法用途**，**请勿用于非法用途**，**请勿用于非法用途**，否则后果自负。
* 用户理解并同意，任何违反法律法规、侵犯他人合法权益的行为，均与本项目及其开发者无关，后果由用户自行承担。

### 2. 使用期限

* 您应该在下载保存，编译使用本项目的24小时内，删除本项目的源代码和（编译出的）程序；超出此期限的任何使用行为，一概与本项目及其开发者无关。

### 3. 操作规范

* 本项目仅允许在授权情况下对数据库进行备份与查看，严禁用于非法目的，否则自行承担所有相关责任；用户如因违反此规定而引发的任何法律责任，将由用户自行承担，与本项目及其开发者无关。
* 严禁用于窃取他人隐私，严禁用于窃取他人隐私，严禁用于窃取他人隐私，否则自行承担所有相关责任。
* 严禁进行二次开发，严禁进行二次开发，严禁进行二次开发，否则自行承担所有相关责任。

### 4. 免责声明接受

* 下载、保存、进一步浏览源代码或者下载安装、编译使用本程序，表示你同意本警告，并承诺遵守它;

### 5. 禁止用于非法测试或渗透

* 禁止利用本项目的相关技术从事非法测试或渗透，禁止利用本项目的相关代码或相关技术从事任何非法工作，如因此产生的一切不良后果与本项目及其开发者无关。
* 任何因此产生的不良后果，包括但不限于数据泄露、系统瘫痪、侵犯隐私等，均与本项目及其开发者无关，责任由用户自行承担。

### 6. 免责声明修改

* 本免责声明可能根据项目运行情况和法律法规的变化进行修改和调整。用户应定期查阅本页面以获取最新版本的免责声明，使用本项目时应遵守最新版本的免责声明。

### 7. 其他

* 除本免责声明规定外，用户在使用本项目过程中应遵守相关的法律法规和道德规范。对于因用户违反相关规定而引发的任何纠纷或损失，本项目及其开发者不承担任何责任。

* 请用户慎重阅读并理解本免责声明的所有内容，确保在使用本项目时严格遵守相关规定。

## 致谢

wechatferry 之所以成为可能，得益于以下项目的灵感:

- [WeChatFerry](https://github.com/lich0821/WeChatFerry)
- [wcf-client-rust](https://github.com/lich0821/wcf-client-rust)
- [node-wcferry](https://github.com/stkevintan/node-wcferry)
- [wechaty](https://github.com/wechaty/wechaty)

## License

基于 MIT 协议 为 💖 发电

[npm-version-src]: https://img.shields.io/npm/v/wechatferry?style=flat&colorA=18181B&colorB=c62828
[npm-version-href]: https://npmjs.com/package/wechatferry
[npm-downloads-src]: https://img.shields.io/npm/dw/wechatferry?style=flat&colorA=18181B&colorB=c62828
[npm-downloads-href]: https://npmjs.com/package/wechatferry
[github-stars-src]: https://img.shields.io/github/stars/wechatferry/wechatferry?style=flat&colorA=18181B&colorB=c62828
[github-stars-href]: https://github.com/wechatferry/wechatferry
[license-src]: https://img.shields.io/github/license/wechatferry/wechatferry.svg?style=flat&colorA=18181B&colorB=c62828
[license-href]: https://github.com/wechatferry/wechatferry/blob/main/LICENSE
[website-src]: https://img.shields.io/badge/文档-Wcferry-18181B?style=flat&colorA=18181B&colorB=c62828
[website-href]: https://wcferry.netlify.app/
