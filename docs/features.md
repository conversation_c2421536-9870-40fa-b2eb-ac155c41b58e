# 特性

本客户端 core 完整实现了 [WechatFerry](https://github.com/lich0821/WeChatFerry) 支持的所有功能，在其基础上 puppet 对如下功能进行了封装：

- 事件
  - 消息事件
  - 加群事件
  - 退群事件
  - 邀请加群事件
  - 修改群名事件
  - 新好友事件
  - *朋友圈事件
- 联系人
  - 获取全部联系人列表
  - 获取某位联系人
  - 获取联系人备注
  - 获取联系人头像
- 消息
  - *获取消息中的名片
  - 下载消息的图片
  - 下载消息的文件
  - 获取消息中的卡片 Url
  - *获取消息中的小程序
  - *获取消息中的地理位置
- 发送
  - 发送文字
  - 发送图片
  - 发送文件
  - 发送卡片Url
  - 转发消息
- 群聊
  - 获取群聊列表
  - 添加/邀请加群
  - 踢人
  - 获取群头像
  - 获取群昵称
  - 获取群公告
  - 群成员列表
- tag
  - 获取联系人 tag 列表

除此之外，agent 还提供了：

- TalkerId 获取
- 历史聊天记录查询

::: info
`*` 开头的功能只对其进行了 xml 解析，并未解密，朋友圈事件只支持获取文字内容，图片信息需要解密
:::
