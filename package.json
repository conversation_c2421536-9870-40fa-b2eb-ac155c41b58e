{"name": "@wechatferry/monorepo", "type": "module", "version": "0.0.26", "private": true, "packageManager": "pnpm@9.8.0+sha512.8e4c3550fb500e808dbc30bb0ce4dd1eb614e30b1c55245f211591ec2cdf9c611cabd34e1364b42f564bd54b3945ed0f49d61d1bbf2ec9bd74b866fcdc723276", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm lint --fix", "build": "rimraf packages/*/dist --glob && pnpm -r --filter=./packages/* run build", "dev": "rimraf packages/*/dist --glob && pnpm -r --filter=./packages/* --filter=!./packages/robot --filter=!./packages/nuxt run dev", "release": "bumpp -r"}, "devDependencies": {"@antfu/eslint-config": "catalog:", "@nuxt/module-builder": "^0.8.4", "@types/fs-extra": "catalog:", "@types/google-protobuf": "^3.15.12", "@types/node": "catalog:", "bumpp": "catalog:", "eslint": "^9.22.0", "esno": "catalog:", "execa": "8.0.1", "fs-extra": "catalog:", "rimraf": "^6.0.1", "taze": "^19.0.2", "unbuild": "catalog:", "vitest": "catalog:"}}