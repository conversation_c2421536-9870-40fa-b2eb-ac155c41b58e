{"name": "@wechatferry/agent", "type": "module", "version": "0.0.26", "description": "wcferry agent client", "author": "mrrhq <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/mrrhq", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "bugs": "https://github.com/wechatferry/wechatferry/issues", "keywords": ["wechat", "wc<PERSON><PERSON>", "robot"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --stub"}, "dependencies": {"@wechatferry/core": "workspace:*", "@wechatferry/logger": "workspace:*", "file-box": "^1.4.15", "knex": "^3.1.0"}}