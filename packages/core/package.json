{"name": "@wechatferry/core", "type": "module", "version": "0.0.26", "description": "A node impl of wcferry client", "author": "mrrhq <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/mrrhq", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "bugs": "https://github.com/wechatferry/wechatferry/issues", "keywords": ["wechat", "wc<PERSON><PERSON>", "robot"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist", "pro", "sdk"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "prepare": "esno scripts/prepare.ts", "gen": "esno scripts/gen-protoc.ts"}, "dependencies": {"@rustup/nng": "^0.1.2", "@wechatferry/logger": "workspace:*", "file-box": "^1.4.15", "fs-extra": "^11.3.0", "google-protobuf": "^3.21.4", "koffi": "^2.10.1", "pathe": "^1.1.2"}, "devDependencies": {"@terascope/fetch-github-release": "^1.0.0", "protoc-gen-ts": "^0.8.7"}, "wcferry": {"version": "39.4.5"}}