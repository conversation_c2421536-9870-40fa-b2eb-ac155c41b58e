/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 5.27.1
 * source: proto/room-data.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
export class RoomData extends pb_1.Message {
    #one_of_decls: number[][] = [];
    constructor(data?: any[] | {
        members?: RoomData.RoomMember[];
        field_2?: number;
        field_3?: number;
        field_4?: number;
        room_capacity?: number;
        field_7?: string;
        field_8?: string;
    }) {
        super();
        pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
        if (!Array.isArray(data) && typeof data == "object") {
            if ("members" in data && data.members != undefined) {
                this.members = data.members;
            }
            if ("field_2" in data && data.field_2 != undefined) {
                this.field_2 = data.field_2;
            }
            if ("field_3" in data && data.field_3 != undefined) {
                this.field_3 = data.field_3;
            }
            if ("field_4" in data && data.field_4 != undefined) {
                this.field_4 = data.field_4;
            }
            if ("room_capacity" in data && data.room_capacity != undefined) {
                this.room_capacity = data.room_capacity;
            }
            if ("field_7" in data && data.field_7 != undefined) {
                this.field_7 = data.field_7;
            }
            if ("field_8" in data && data.field_8 != undefined) {
                this.field_8 = data.field_8;
            }
        }
    }
    get members() {
        return pb_1.Message.getRepeatedWrapperField(this, RoomData.RoomMember, 1) as RoomData.RoomMember[];
    }
    set members(value: RoomData.RoomMember[]) {
        pb_1.Message.setRepeatedWrapperField(this, 1, value);
    }
    get field_2() {
        return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
    }
    set field_2(value: number) {
        pb_1.Message.setField(this, 2, value);
    }
    get field_3() {
        return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
    }
    set field_3(value: number) {
        pb_1.Message.setField(this, 3, value);
    }
    get field_4() {
        return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
    }
    set field_4(value: number) {
        pb_1.Message.setField(this, 4, value);
    }
    get room_capacity() {
        return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
    }
    set room_capacity(value: number) {
        pb_1.Message.setField(this, 5, value);
    }
    get field_7() {
        return pb_1.Message.getFieldWithDefault(this, 7, "0") as string;
    }
    set field_7(value: string) {
        pb_1.Message.setField(this, 7, value);
    }
    get field_8() {
        return pb_1.Message.getFieldWithDefault(this, 8, "0") as string;
    }
    set field_8(value: string) {
        pb_1.Message.setField(this, 8, value);
    }
    static fromObject(data: {
        members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
        field_2?: number;
        field_3?: number;
        field_4?: number;
        room_capacity?: number;
        field_7?: string;
        field_8?: string;
    }): RoomData {
        const message = new RoomData({});
        if (data.members != null) {
            message.members = data.members.map(item => RoomData.RoomMember.fromObject(item));
        }
        if (data.field_2 != null) {
            message.field_2 = data.field_2;
        }
        if (data.field_3 != null) {
            message.field_3 = data.field_3;
        }
        if (data.field_4 != null) {
            message.field_4 = data.field_4;
        }
        if (data.room_capacity != null) {
            message.room_capacity = data.room_capacity;
        }
        if (data.field_7 != null) {
            message.field_7 = data.field_7;
        }
        if (data.field_8 != null) {
            message.field_8 = data.field_8;
        }
        return message;
    }
    toObject() {
        const data: {
            members?: ReturnType<typeof RoomData.RoomMember.prototype.toObject>[];
            field_2?: number;
            field_3?: number;
            field_4?: number;
            room_capacity?: number;
            field_7?: string;
            field_8?: string;
        } = {};
        if (this.members != null) {
            data.members = this.members.map((item: RoomData.RoomMember) => item.toObject());
        }
        if (this.field_2 != null) {
            data.field_2 = this.field_2;
        }
        if (this.field_3 != null) {
            data.field_3 = this.field_3;
        }
        if (this.field_4 != null) {
            data.field_4 = this.field_4;
        }
        if (this.room_capacity != null) {
            data.room_capacity = this.room_capacity;
        }
        if (this.field_7 != null) {
            data.field_7 = this.field_7;
        }
        if (this.field_8 != null) {
            data.field_8 = this.field_8;
        }
        return data;
    }
    serialize(): Uint8Array;
    serialize(w: pb_1.BinaryWriter): void;
    serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
        const writer = w || new pb_1.BinaryWriter();
        if (this.members.length)
            writer.writeRepeatedMessage(1, this.members, (item: RoomData.RoomMember) => item.serialize(writer));
        if (this.field_2 != 0)
            writer.writeInt32(2, this.field_2);
        if (this.field_3 != 0)
            writer.writeInt32(3, this.field_3);
        if (this.field_4 != 0)
            writer.writeInt32(4, this.field_4);
        if (this.room_capacity != 0)
            writer.writeInt32(5, this.room_capacity);
        if (this.field_7 != "0")
            writer.writeInt64String(7, this.field_7);
        if (this.field_8 != "0")
            writer.writeInt64String(8, this.field_8);
        if (!w)
            return writer.getResultBuffer();
    }
    static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomData {
        const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RoomData();
        while (reader.nextField()) {
            if (reader.isEndGroup())
                break;
            switch (reader.getFieldNumber()) {
                case 1:
                    reader.readMessage(message.members, () => pb_1.Message.addToRepeatedWrapperField(message, 1, RoomData.RoomMember.deserialize(reader), RoomData.RoomMember));
                    break;
                case 2:
                    message.field_2 = reader.readInt32();
                    break;
                case 3:
                    message.field_3 = reader.readInt32();
                    break;
                case 4:
                    message.field_4 = reader.readInt32();
                    break;
                case 5:
                    message.room_capacity = reader.readInt32();
                    break;
                case 7:
                    message.field_7 = reader.readInt64String();
                    break;
                case 8:
                    message.field_8 = reader.readInt64String();
                    break;
                default: reader.skipField();
            }
        }
        return message;
    }
    serializeBinary(): Uint8Array {
        return this.serialize();
    }
    static deserializeBinary(bytes: Uint8Array): RoomData {
        return RoomData.deserialize(bytes);
    }
}
export namespace RoomData {
    export class RoomMember extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wxid?: string;
            name?: string;
            state?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wxid" in data && data.wxid != undefined) {
                    this.wxid = data.wxid;
                }
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("state" in data && data.state != undefined) {
                    this.state = data.state;
                }
            }
        }
        get wxid() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set wxid(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get state() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set state(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            wxid?: string;
            name?: string;
            state?: number;
        }): RoomMember {
            const message = new RoomMember({});
            if (data.wxid != null) {
                message.wxid = data.wxid;
            }
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.state != null) {
                message.state = data.state;
            }
            return message;
        }
        toObject() {
            const data: {
                wxid?: string;
                name?: string;
                state?: number;
            } = {};
            if (this.wxid != null) {
                data.wxid = this.wxid;
            }
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.state != null) {
                data.state = this.state;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.wxid.length)
                writer.writeString(1, this.wxid);
            if (this.name.length)
                writer.writeString(2, this.name);
            if (this.state != 0)
                writer.writeInt32(3, this.state);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RoomMember {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RoomMember();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.wxid = reader.readString();
                        break;
                    case 2:
                        message.name = reader.readString();
                        break;
                    case 3:
                        message.state = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RoomMember {
            return RoomMember.deserialize(bytes);
        }
    }
}
