{"name": "@wechatferry/plugins", "type": "module", "version": "0.0.26", "description": "wechatferry plugins", "author": "mrrhq <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/mrrhq", "homepage": "https://github.com/wechatferry/wechatferry#readme", "repository": {"type": "git", "url": "https://github.com/wechatferry/wechatferry"}, "bugs": "https://github.com/wechatferry/wechatferry/issues", "keywords": ["wechat", "wc<PERSON><PERSON>", "robot", "plugins"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --stub"}, "dependencies": {"@wechatferry/core": "workspace:*", "dayjs": "^1.11.13", "file-box": "^1.4.15", "knex": "^3.1.0", "p-throttle": "^6.2.0", "unstorage": "^1.15.0", "wechaty": "^1.20.2", "wechaty-plugin-contrib": "^1.12.2", "wechaty-puppet": "^1.20.2"}}