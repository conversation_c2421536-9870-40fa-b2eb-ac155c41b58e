{"compilerOptions": {"target": "es2018", "jsx": "preserve", "lib": ["esnext"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "types": ["node"], "strict": true, "strictNullChecks": true, "esModuleInterop": true, "skipDefaultLibCheck": true, "skipLibCheck": true}, "exclude": ["**/dist/**", "**/node_modules/**", "**/client/**", "**/playground/**", "**/examples/**", "**/fixtures/**", "**/interactive/**", "**/test/dts/**"]}